import { logging } from "@skywind-group/sw-utils";
import { Notificator, EmptyPoolAlert, EntityDomainBlockedAlert } from "./types";

export class EmailNotificator implements Notificator {
    private readonly log: any;

    constructor() {
        this.log = logging.logger("email-notificator");
    }

    public async alertEmptyPool(alert: EmptyPoolAlert): Promise<void> {
        const subject = `Empty Domain Pool Alert - ${alert.poolName}`;
        const body = `
Domain Pool Alert

Pool: ${alert.poolName} (ID: ${alert.poolId})
Pool Type: ${alert.poolType}
Impact: No domains available for new assignments
Action Required: Add more domains to this pool or check domain statuses

This is an automated alert from the Domain Monitor system.
        `.trim();

        await this.sendEmail(subject, body);
    }

    public async alertEntityDomainBlocked(alert: EntityDomainBlockedAlert): Promise<void> {
        const timestamp = alert.blockedAt.toISOString();
        const subject = `Domain Blocked Alert - ${alert.domain}`;
        const body = `
Default Domain Blocked Alert

Domain: ${alert.domain}
Domain ID: ${alert.domainId}
Domain Type: ${alert.staticType}
Blocked At: ${timestamp}
Impact: Default domain is no longer accessible

This is an automated alert from the Domain Monitor system.
        `.trim();

        await this.sendEmail(subject, body);
    }

    private async sendEmail(subject: string, body: string): Promise<void> {
        // TODO: Implement email sending logic
        this.log.info({ subject, body }, "Email notification would be sent");
    }
}
